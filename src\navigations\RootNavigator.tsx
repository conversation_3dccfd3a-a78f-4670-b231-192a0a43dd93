import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';

import { Colors } from '../constants';
import { RootStackParamList } from './types';
import { BottomTabNavigator } from './BottomTabNavigator';

// Import screens that will be presented modally or as full screen
import { ChatDetailScreen } from '../screens/ChatDetailScreen';
import { SettingsScreen } from '../screens/SettingsScreen';
import { EditProfileScreen } from '../screens/EditProfileScreen';

const Stack = createNativeStackNavigator<RootStackParamList>();

export const RootNavigator: React.FC = () => {
  return (
    <NavigationContainer>
      <StatusBar style="auto" />
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: Colors.background,
          },
          headerTintColor: Colors.text,
          headerTitleStyle: {
            fontWeight: '600',
            fontSize: 18,
          },
          headerShadowVisible: true,
          animation: 'slide_from_right',
        }}
      >
        {/* Main Tab Navigator */}
        <Stack.Screen
          name="Main"
          component={BottomTabNavigator}
          options={{
            headerShown: false,
          }}
        />

        {/* Chat Detail Screen */}
        <Stack.Screen
          name="Chat"
          component={ChatDetailScreen}
          options={({ route }) => ({
            title: route.params.chatName,
            headerBackTitleVisible: false,
            headerTitleAlign: 'center',
            animation: 'slide_from_right',
          })}
        />

        {/* Settings Screen */}
        <Stack.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            title: 'Settings',
          //  headerBackTitleVisible: false,
            presentation: 'modal',
            animation: 'slide_from_bottom',
          }}
        />

        {/* Edit Profile Screen */}
        <Stack.Screen
          name="EditProfile"
          component={EditProfileScreen}
          options={{
            title: 'Edit Profile',
            //headerBackTitleVisible: false,
            presentation: 'modal',
            animation: 'slide_from_bottom',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};
