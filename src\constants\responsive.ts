import { Dimensions, PixelRatio, Platform } from 'react-native';

// Get device dimensions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Base dimensions (iPhone 6/7/8 as reference)
const BASE_WIDTH = 375;
const BASE_HEIGHT = 667;

// Responsive width function
export const wp = (percentage: number): number => {
  const value = (percentage * SCREEN_WIDTH) / 100;
  return Math.round(PixelRatio.roundToNearestPixel(value));
};

// Responsive height function
export const hp = (percentage: number): number => {
  const value = (percentage * SCREEN_HEIGHT) / 100;
  return Math.round(PixelRatio.roundToNearestPixel(value));
};

// Responsive font size
export const rf = (size: number): number => {
  const scale = SCREEN_WIDTH / BASE_WIDTH;
  const newSize = size * scale;
  
  if (Platform.OS === 'ios') {
    return Math.round(PixelRatio.roundToNearestPixel(newSize));
  } else {
    return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 2;
  }
};

// Responsive spacing
export const rs = (size: number): number => {
  const scale = Math.min(SCREEN_WIDTH / BASE_WIDTH, SCREEN_HEIGHT / BASE_HEIGHT);
  const newSize = size * scale;
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
};

// Device type detection
export const isTablet = (): boolean => {
  const pixelDensity = PixelRatio.get();
  const adjustedWidth = SCREEN_WIDTH * pixelDensity;
  const adjustedHeight = SCREEN_HEIGHT * pixelDensity;
  
  if (pixelDensity < 2 && (adjustedWidth >= 1000 || adjustedHeight >= 1000)) {
    return true;
  } else if (pixelDensity === 2 && (adjustedWidth >= 1920 || adjustedHeight >= 1920)) {
    return true;
  } else {
    return false;
  }
};

// Screen size categories
export const getScreenSize = (): 'small' | 'medium' | 'large' => {
  if (SCREEN_WIDTH < 375) return 'small';
  if (SCREEN_WIDTH < 414) return 'medium';
  return 'large';
};

// Orientation detection
export const isLandscape = (): boolean => {
  return SCREEN_WIDTH > SCREEN_HEIGHT;
};

// Safe area helpers
export const getSafeAreaPadding = () => {
  const isIOS = Platform.OS === 'ios';
  const isIPhoneX = isIOS && (SCREEN_HEIGHT >= 812 || SCREEN_WIDTH >= 812);
  
  return {
    top: isIPhoneX ? 44 : isIOS ? 20 : 0,
    bottom: isIPhoneX ? 34 : 0,
  };
};

// Responsive breakpoints
export const ResponsiveBreakpoints = {
  small: 320,
  medium: 375,
  large: 414,
  tablet: 768,
  desktop: 1024,
};

// Check if screen matches breakpoint
export const matchesBreakpoint = (breakpoint: keyof typeof ResponsiveBreakpoints): boolean => {
  return SCREEN_WIDTH >= ResponsiveBreakpoints[breakpoint];
};

// Responsive value selector
export const responsiveValue = <T>(values: {
  small?: T;
  medium?: T;
  large?: T;
  tablet?: T;
  default: T;
}): T => {
  if (isTablet() && values.tablet) return values.tablet;
  
  const screenSize = getScreenSize();
  
  if (screenSize === 'large' && values.large) return values.large;
  if (screenSize === 'medium' && values.medium) return values.medium;
  if (screenSize === 'small' && values.small) return values.small;
  
  return values.default;
};

// Responsive grid columns
export const getGridColumns = (): number => {
  return responsiveValue({
    small: 1,
    medium: 2,
    large: 2,
    tablet: 3,
    default: 2,
  });
};

// Responsive padding
export const getResponsivePadding = () => {
  return responsiveValue({
    small: 12,
    medium: 16,
    large: 20,
    tablet: 24,
    default: 16,
  });
};

// Responsive margin
export const getResponsiveMargin = () => {
  return responsiveValue({
    small: 8,
    medium: 12,
    large: 16,
    tablet: 20,
    default: 12,
  });
};

// Responsive font sizes
export const ResponsiveFontSizes = {
  xs: responsiveValue({ small: 10, medium: 11, large: 12, default: 11 }),
  sm: responsiveValue({ small: 12, medium: 13, large: 14, default: 13 }),
  base: responsiveValue({ small: 14, medium: 15, large: 16, default: 15 }),
  md: responsiveValue({ small: 16, medium: 17, large: 18, default: 17 }),
  lg: responsiveValue({ small: 18, medium: 19, large: 20, default: 19 }),
  xl: responsiveValue({ small: 20, medium: 22, large: 24, default: 22 }),
  '2xl': responsiveValue({ small: 24, medium: 26, large: 28, default: 26 }),
  '3xl': responsiveValue({ small: 28, medium: 30, large: 32, default: 30 }),
  '4xl': responsiveValue({ small: 32, medium: 36, large: 40, default: 36 }),
};

// Device info
export const DeviceInfo = {
  width: SCREEN_WIDTH,
  height: SCREEN_HEIGHT,
  isTablet: isTablet(),
  isLandscape: isLandscape(),
  screenSize: getScreenSize(),
  pixelRatio: PixelRatio.get(),
  fontScale: PixelRatio.getFontScale(),
  safeArea: getSafeAreaPadding(),
};

// Responsive utilities object
export const Responsive = {
  wp,
  hp,
  rf,
  rs,
  isTablet,
  getScreenSize,
  isLandscape,
  getSafeAreaPadding,
  matchesBreakpoint,
  responsiveValue,
  getGridColumns,
  getResponsivePadding,
  getResponsiveMargin,
  ResponsiveFontSizes,
  DeviceInfo,
  Breakpoints: ResponsiveBreakpoints,
};

export default Responsive;
