import React, { useState, forwardRef } from 'react';
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Typography, BorderRadius, Layout, Spacing } from '../constants';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: string;
  rightIcon?: string;
  onRightIconPress?: () => void;
  variant?: 'default' | 'filled' | 'outline';
  size?: 'small' | 'medium' | 'large';
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  hintStyle?: TextStyle;
  disabled?: boolean;
  required?: boolean;
}

export const Input = forwardRef<TextInput, InputProps>(({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  onRightIconPress,
  variant = 'default',
  size = 'medium',
  containerStyle,
  inputStyle,
  labelStyle,
  errorStyle,
  hintStyle,
  disabled = false,
  required = false,
  style,
  ...props
}, ref) => {
  const [isFocused, setIsFocused] = useState(false);

  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      ...styles.container,
      ...styles[`${size}Container` as keyof typeof styles],
    };

    switch (variant) {
      case 'filled':
        return { ...baseStyle, ...styles.filledContainer };
      case 'outline':
        return { ...baseStyle, ...styles.outlineContainer };
      default:
        return { ...baseStyle, ...styles.defaultContainer };
    }
  };

  const getInputContainerStyle = (): ViewStyle => {
    let baseStyle: ViewStyle = {
      ...styles.inputContainer,
      ...styles[`${size}InputContainer` as keyof typeof styles],
    };

    if (isFocused) {
      baseStyle = { ...baseStyle, ...styles.focused };
    }

    if (error) {
      baseStyle = { ...baseStyle, ...styles.errorContainer };
    }

    if (disabled) {
      baseStyle = { ...baseStyle, ...styles.disabledContainer };
    }

    return baseStyle;
  };

  const getInputStyle = (): TextStyle => {
    return {
      ...styles.input,
      ...styles[`${size}Input` as keyof typeof styles],
      ...(leftIcon && styles.inputWithLeftIcon),
      ...(rightIcon && styles.inputWithRightIcon),
    };
  };

  return (
    <View style={[getContainerStyle(), containerStyle]}>
      {label && (
        <Text style={[styles.label, labelStyle]}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      
      <View style={[getInputContainerStyle(), style]}>
        {leftIcon && (
          <Ionicons
            name={leftIcon as any}
            size={Layout.icon}
            color={isFocused ? Colors.primary : Colors.textSecondary}
            style={styles.leftIcon}
          />
        )}
        
        <TextInput
          ref={ref}
          style={[getInputStyle(), inputStyle]}
          placeholderTextColor={Colors.textPlaceholder}
          editable={!disabled}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />
        
        {rightIcon && (
          <TouchableOpacity
            onPress={onRightIconPress}
            style={styles.rightIconContainer}
            disabled={!onRightIconPress}
          >
            <Ionicons
              name={rightIcon as any}
              size={Layout.icon}
              color={isFocused ? Colors.primary : Colors.textSecondary}
            />
          </TouchableOpacity>
        )}
      </View>
      
      {error && (
        <Text style={[styles.error, errorStyle]}>{error}</Text>
      )}
      
      {hint && !error && (
        <Text style={[styles.hint, hintStyle]}>{hint}</Text>
      )}
    </View>
  );
});

Input.displayName = 'Input';

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.md,
  },
  
  // Container sizes
  smallContainer: {},
  mediumContainer: {},
  largeContainer: {},
  
  // Container variants
  defaultContainer: {},
  filledContainer: {},
  outlineContainer: {},
  
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: BorderRadius.input,
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: Colors.background,
  },
  
  // Input container sizes
  smallInputContainer: {
    height: Layout.inputHeightSmall,
    paddingHorizontal: 12,
  },
  mediumInputContainer: {
    height: Layout.inputHeight,
    paddingHorizontal: 16,
  },
  largeInputContainer: {
    height: Layout.inputHeightLarge,
    paddingHorizontal: 20,
  },
  
  input: {
    flex: 1,
    ...Typography.body,
    color: Colors.text,
    padding: 0,
  },
  
  // Input sizes
  smallInput: {
    ...Typography.bodySmall,
  },
  mediumInput: {
    ...Typography.body,
  },
  largeInput: {
    ...Typography.bodyLarge,
  },
  
  inputWithLeftIcon: {
    marginLeft: Spacing.sm,
  },
  
  inputWithRightIcon: {
    marginRight: Spacing.sm,
  },
  
  leftIcon: {
    marginRight: 0,
  },
  
  rightIconContainer: {
    padding: 4,
  },
  
  label: {
    ...Typography.captionBold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  
  required: {
    color: Colors.error,
  },
  
  error: {
    ...Typography.caption,
    color: Colors.error,
    marginTop: Spacing.xs,
  },
  
  hint: {
    ...Typography.caption,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  
  // States
  focused: {
    borderColor: Colors.primary,
    borderWidth: 2,
  },
  
  errorContainer: {
    borderColor: Colors.error,
  },
  
  disabledContainer: {
    backgroundColor: Colors.backgroundSecondary,
    opacity: 0.6,
  },
});
