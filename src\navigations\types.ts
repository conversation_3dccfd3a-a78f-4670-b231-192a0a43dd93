import { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

// Root Stack Navigator
export type RootStackParamList = {
  Main: undefined;
  Chat: {
    chatId: string;
    chatName: string;
    avatar?: string;
  };
  Profile: undefined;
  Settings: undefined;
  EditProfile: undefined;
};

// Bottom Tab Navigator
export type BottomTabParamList = {
  Chats: undefined;
  Profile: undefined;
  SocialFeed: undefined;
};

// Chat Stack Navigator
export type ChatStackParamList = {
  ChatList: undefined;
  ChatDetail: {
    chatId: string;
    chatName: string;
    avatar?: string;
  };
};

// Profile Stack Navigator
export type ProfileStackParamList = {
  ProfileMain: undefined;
  Settings: undefined;
  EditProfile: undefined;
};

// Screen Props Types
export type RootStackScreenProps<Screen extends keyof RootStackParamList> =
  NativeStackScreenProps<RootStackParamList, Screen>;

export type BottomTabScreenProps<Screen extends keyof BottomTabParamList> =
  CompositeScreenProps<
    BottomTabScreenProps<BottomTabParamList, Screen>,
    NativeStackScreenProps<RootStackParamList>
  >;

export type ChatStackScreenProps<Screen extends keyof ChatStackParamList> =
  CompositeScreenProps<
    NativeStackScreenProps<ChatStackParamList, Screen>,
    BottomTabScreenProps<keyof BottomTabParamList>
  >;

export type ProfileStackScreenProps<Screen extends keyof ProfileStackParamList> =
  CompositeScreenProps<
    NativeStackScreenProps<ProfileStackParamList, Screen>,
    BottomTabScreenProps<keyof BottomTabParamList>
  >;

// Navigation Props
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
