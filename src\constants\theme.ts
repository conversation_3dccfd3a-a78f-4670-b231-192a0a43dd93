import { Colors, LightTheme, DarkTheme } from './colors';
import { Typography } from './typography';
import { Spacing, Padding, Margin, BorderRadius, Shadow, Layout } from './spacing';

// Theme interface
export interface Theme {
  colors: typeof LightTheme;
  typography: typeof Typography;
  spacing: typeof Spacing;
  padding: typeof Padding;
  margin: typeof Margin;
  borderRadius: typeof BorderRadius;
  shadow: typeof Shadow;
  layout: typeof Layout;
  isDark: boolean;
}

// Light theme configuration
export const lightTheme: Theme = {
  colors: LightTheme,
  typography: Typography,
  spacing: Spacing,
  padding: Padding,
  margin: Margin,
  borderRadius: BorderRadius,
  shadow: Shadow,
  layout: Layout,
  isDark: false,
};

// Dark theme configuration
export const darkTheme: Theme = {
  colors: DarkTheme,
  typography: Typography,
  spacing: Spacing,
  padding: Padding,
  margin: Margin,
  borderRadius: BorderRadius,
  shadow: Shadow,
  layout: Layout,
  isDark: true,
};

// Theme context type
export type ThemeContextType = {
  theme: Theme;
  isDark: boolean;
  toggleTheme: () => void;
  setTheme: (isDark: boolean) => void;
};

// Responsive breakpoints
export const Breakpoints = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1400,
};

// Device size helpers
export const DeviceSize = {
  isSmall: (width: number) => width < Breakpoints.sm,
  isMedium: (width: number) => width >= Breakpoints.sm && width < Breakpoints.lg,
  isLarge: (width: number) => width >= Breakpoints.lg,
  isTablet: (width: number) => width >= Breakpoints.md,
};

// Animation durations
export const AnimationDuration = {
  fast: 150,
  normal: 250,
  slow: 350,
  slower: 500,
};

// Animation easing
export const AnimationEasing = {
  linear: 'linear',
  ease: 'ease',
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',
};

// Z-index levels
export const ZIndex = {
  hide: -1,
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
};

// Common component styles
export const CommonStyles = {
  // Flex layouts
  flex1: { flex: 1 },
  flexRow: { flexDirection: 'row' as const },
  flexColumn: { flexDirection: 'column' as const },
  flexCenter: { 
    justifyContent: 'center' as const, 
    alignItems: 'center' as const 
  },
  flexBetween: { 
    justifyContent: 'space-between' as const, 
    alignItems: 'center' as const 
  },
  flexStart: { 
    justifyContent: 'flex-start' as const, 
    alignItems: 'center' as const 
  },
  flexEnd: { 
    justifyContent: 'flex-end' as const, 
    alignItems: 'center' as const 
  },
  
  // Positioning
  absolute: { position: 'absolute' as const },
  relative: { position: 'relative' as const },
  
  // Common containers
  container: {
    flex: 1,
    paddingHorizontal: Padding.screen,
  },
  safeContainer: {
    flex: 1,
    paddingHorizontal: Padding.screen,
    paddingTop: Spacing.lg,
  },
  
  // Common cards
  card: {
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.card,
    padding: Padding.card,
    ...Shadow.sm,
  },
  
  // Common buttons
  button: {
    height: Layout.buttonHeight,
    borderRadius: BorderRadius.button,
    paddingHorizontal: Padding.buttonHorizontal,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  
  // Common inputs
  input: {
    height: Layout.inputHeight,
    borderRadius: BorderRadius.input,
    paddingHorizontal: Padding.input,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  
  // Text styles
  textCenter: { textAlign: 'center' as const },
  textLeft: { textAlign: 'left' as const },
  textRight: { textAlign: 'right' as const },
  
  // Overflow
  hidden: { overflow: 'hidden' as const },
  
  // Opacity
  opacity50: { opacity: 0.5 },
  opacity75: { opacity: 0.75 },
  opacity90: { opacity: 0.9 },
};

export default {
  lightTheme,
  darkTheme,
  Colors,
  Typography,
  Spacing,
  Padding,
  Margin,
  BorderRadius,
  Shadow,
  Layout,
  Breakpoints,
  DeviceSize,
  AnimationDuration,
  AnimationEasing,
  ZIndex,
  CommonStyles,
};
