import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    Image,
    Platform,
    ScrollView,
    StyleSheet,
    Switch,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from '../constants/colors';
import {
  SPACING,
  FONT_SIZES,
  BORDER_RADIUS,
  ICON_SIZES,
  isTablet
} from '../constants/dimensions';
import ResponsiveContainer from '../components/ResponsiveContainer';

export default function ProfileScreen() {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [isOnline, setIsOnline] = useState(true);

  const profileData = {
    name: '<PERSON>',
    username: '@alexjohnson',
    email: '<EMAIL>',
    phone: '+****************',
    bio: 'Mobile app developer passionate about creating amazing user experiences. Love to chat about tech, design, and life! 🚀',
    avatar: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=200&h=200&fit=crop&crop=face',
    joinDate: 'Joined March 2023',
    stats: {
      chats: 42,
      groups: 8,
      favorites: 12,
    },
  };

  const menuItems = [
    {
      id: 'account',
      title: 'Account Settings',
      icon: 'person-outline',
      color: '#007AFF',
    },
    {
      id: 'privacy',
      title: 'Privacy & Security',
      icon: 'shield-outline',
      color: '#34C759',
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: 'notifications-outline',
      color: '#FF9500',
    },
    {
      id: 'storage',
      title: 'Storage & Data',
      icon: 'folder-outline',
      color: '#5856D6',
    },
    {
      id: 'help',
      title: 'Help & Support',
      icon: 'help-circle-outline',
      color: '#FF3B30',
    },
    {
      id: 'about',
      title: 'About',
      icon: 'information-circle-outline',
      color: '#8E8E93',
    },
  ];

  const renderMenuItem = (item: typeof menuItems[0]) => (
    <TouchableOpacity key={item.id} style={styles.menuItem} activeOpacity={0.7}>
      <View style={[styles.menuIcon, { backgroundColor: `${item.color}15` }]}>
        <Ionicons name={item.icon as any} size={width * 0.055} color={item.color} />
      </View>
      <Text style={styles.menuTitle}>{item.title}</Text>
      <Ionicons name="chevron-forward" size={width * 0.045} color="#C7C7CC" />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Profile</Text>
          <TouchableOpacity style={styles.editButton}>
            <Text style={styles.editButtonText}>Edit</Text>
          </TouchableOpacity>
        </View>

        {/* Profile Info */}
        <View style={styles.profileSection}>
          <View style={styles.avatarContainer}>
            <Image source={{ uri: profileData.avatar }} style={styles.avatar} />
            <View style={[styles.statusIndicator, { backgroundColor: isOnline ? '#34C759' : '#8E8E93' }]} />
          </View>
          
          <Text style={styles.name}>{profileData.name}</Text>
          <Text style={styles.username}>{profileData.username}</Text>
          <Text style={styles.bio}>{profileData.bio}</Text>
          <Text style={styles.joinDate}>{profileData.joinDate}</Text>

          {/* Stats */}
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{profileData.stats.chats}</Text>
              <Text style={styles.statLabel}>Chats</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{profileData.stats.groups}</Text>
              <Text style={styles.statLabel}>Groups</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>{profileData.stats.favorites}</Text>
              <Text style={styles.statLabel}>Favorites</Text>
            </View>
          </View>
        </View>

        {/* Quick Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Settings</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, { backgroundColor: '#34C75915' }]}>
                <Ionicons name="moon-outline" size={width * 0.055} color="#34C759" />
              </View>
              <Text style={styles.settingTitle}>Dark Mode</Text>
            </View>
            <Switch
              value={isDarkMode}
              onValueChange={setIsDarkMode}
              trackColor={{ false: '#E5E5EA', true: '#34C759' }}
              thumbColor="#FFFFFF"
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, { backgroundColor: '#FF950015' }]}>
                <Ionicons name="notifications-outline" size={width * 0.055} color="#FF9500" />
              </View>
              <Text style={styles.settingTitle}>Notifications</Text>
            </View>
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: '#E5E5EA', true: '#34C759' }}
              thumbColor="#FFFFFF"
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingLeft}>
              <View style={[styles.settingIcon, { backgroundColor: '#007AFF15' }]}>
                <Ionicons name="wifi-outline" size={width * 0.055} color="#007AFF" />
              </View>
              <Text style={styles.settingTitle}>Online Status</Text>
            </View>
            <Switch
              value={isOnline}
              onValueChange={setIsOnline}
              trackColor={{ false: '#E5E5EA', true: '#34C759' }}
              thumbColor="#FFFFFF"
            />
          </View>
        </View>

        {/* Menu Items */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Settings</Text>
          <View style={styles.menuContainer}>
            {menuItems.map(renderMenuItem)}
          </View>
        </View>

        {/* Logout Button */}
        <View style={styles.section}>
          <TouchableOpacity style={styles.logoutButton} activeOpacity={0.7}>
            <Text style={styles.logoutText}>Sign Out</Text>
          </TouchableOpacity>
        </View>

        {/* Version Info */}
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>ChatApp v1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: width * 0.05,
    paddingTop: Platform.OS === 'ios' ? height * 0.01 : height * 0.02,
    paddingBottom: height * 0.02,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  headerTitle: {
    fontSize: width * 0.08,
    fontWeight: 'bold',
    color: '#000000',
  },
  editButton: {
    paddingHorizontal: width * 0.04,
    paddingVertical: height * 0.01,
  },
  editButtonText: {
    fontSize: width * 0.042,
    color: '#007AFF',
    fontWeight: '600',
  },
  profileSection: {
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    paddingVertical: height * 0.04,
    marginBottom: height * 0.02,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: height * 0.02,
  },
  avatar: {
    width: width * 0.25,
    height: width * 0.25,
    borderRadius: width * 0.125,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: width * 0.02,
    right: width * 0.02,
    width: width * 0.06,
    height: width * 0.06,
    borderRadius: width * 0.03,
    borderWidth: 3,
    borderColor: '#FFFFFF',
  },
  name: {
    fontSize: width * 0.06,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: height * 0.005,
  },
  username: {
    fontSize: width * 0.04,
    color: '#007AFF',
    marginBottom: height * 0.015,
  },
  bio: {
    fontSize: width * 0.038,
    color: '#8E8E93',
    textAlign: 'center',
    paddingHorizontal: width * 0.08,
    lineHeight: width * 0.055,
    marginBottom: height * 0.015,
  },
  joinDate: {
    fontSize: width * 0.035,
    color: '#C7C7CC',
    marginBottom: height * 0.025,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: width * 0.055,
    fontWeight: 'bold',
    color: '#000000',
  },
  statLabel: {
    fontSize: width * 0.035,
    color: '#8E8E93',
    marginTop: height * 0.005,
  },
  statDivider: {
    width: 1,
    height: height * 0.04,
    backgroundColor: '#E5E5EA',
    marginHorizontal: width * 0.05,
  },
  section: {
    marginBottom: height * 0.02,
  },
  sectionTitle: {
    fontSize: width * 0.038,
    fontWeight: '600',
    color: '#8E8E93',
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.015,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.018,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: width * 0.08,
    height: width * 0.08,
    borderRadius: width * 0.02,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: width * 0.04,
  },
  settingTitle: {
    fontSize: width * 0.042,
    color: '#000000',
  },
  menuContainer: {
    backgroundColor: '#FFFFFF',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.018,
    borderBottomWidth: 0.5,
    borderBottomColor: '#E5E5EA',
  },
  menuIcon: {
    width: width * 0.08,
    height: width * 0.08,
    borderRadius: width * 0.02,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: width * 0.04,
  },
  menuTitle: {
    fontSize: width * 0.042,
    color: '#000000',
    flex: 1,
  },
  logoutButton: {
    backgroundColor: '#FFFFFF',
    paddingVertical: height * 0.02,
    alignItems: 'center',
    borderRadius: width * 0.03,
    marginHorizontal: width * 0.05,
  },
  logoutText: {
    fontSize: width * 0.042,
    color: '#FF3B30',
    fontWeight: '600',
  },
  versionContainer: {
    alignItems: 'center',
    paddingVertical: height * 0.03,
  },
  versionText: {
    fontSize: width * 0.035,
    color: '#C7C7CC',
  },
});
