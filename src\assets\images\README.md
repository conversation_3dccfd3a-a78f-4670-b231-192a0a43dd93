# Assets Directory

This directory contains all static assets for the ChatApp.

## Structure

- `images/` - Image assets (logos, icons, illustrations)
- `fonts/` - Custom font files (if any)
- `data/` - Mock data files and JSON fixtures

## Usage

Import assets using relative paths:

```typescript
// For images
import logo from '../assets/images/logo.png';

// For data
import mockData from '../assets/data/mockChats.json';
```

## Guidelines

- Use descriptive filenames
- Optimize images for mobile (WebP format preferred)
- Keep file sizes minimal for better performance
- Use consistent naming conventions (camelCase or kebab-case)
