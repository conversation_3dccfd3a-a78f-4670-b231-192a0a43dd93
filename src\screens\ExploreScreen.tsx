import React from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function ExploreScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Text style={styles.title}>Explore 🔍</Text>
          <Text style={styles.subtitle}>Discover new features and connections</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Discover People</Text>
          <View style={styles.peopleGrid}>
            <View style={styles.personCard}>
              <Text style={styles.personAvatar}>👨‍💻</Text>
              <Text style={styles.personName}><PERSON></Text>
              <Text style={styles.personRole}>Developer</Text>
              <TouchableOpacity style={styles.connectButton}>
                <Text style={styles.connectButtonText}>Connect</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.personCard}>
              <Text style={styles.personAvatar}>👩‍🎨</Text>
              <Text style={styles.personName}><PERSON></Text>
              <Text style={styles.personRole}>Designer</Text>
              <TouchableOpacity style={styles.connectButton}>
                <Text style={styles.connectButtonText}>Connect</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Popular Groups</Text>
          <View style={styles.groupItem}>
            <Text style={styles.groupIcon}>💻</Text>
            <View style={styles.groupInfo}>
              <Text style={styles.groupName}>React Native Developers</Text>
              <Text style={styles.groupMembers}>1,234 members</Text>
            </View>
            <TouchableOpacity style={styles.joinButton}>
              <Text style={styles.joinButtonText}>Join</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.groupItem}>
            <Text style={styles.groupIcon}>🎨</Text>
            <View style={styles.groupInfo}>
              <Text style={styles.groupName}>UI/UX Designers</Text>
              <Text style={styles.groupMembers}>856 members</Text>
            </View>
            <TouchableOpacity style={styles.joinButton}>
              <Text style={styles.joinButtonText}>Join</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.groupItem}>
            <Text style={styles.groupIcon}>🚀</Text>
            <View style={styles.groupInfo}>
              <Text style={styles.groupName}>Startup Founders</Text>
              <Text style={styles.groupMembers}>432 members</Text>
            </View>
            <TouchableOpacity style={styles.joinButton}>
              <Text style={styles.joinButtonText}>Join</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Trending Topics</Text>
          <View style={styles.topicsContainer}>
            <View style={styles.topicTag}>
              <Text style={styles.topicText}>#ReactNative</Text>
            </View>
            <View style={styles.topicTag}>
              <Text style={styles.topicText}>#MobileApp</Text>
            </View>
            <View style={styles.topicTag}>
              <Text style={styles.topicText}>#JavaScript</Text>
            </View>
            <View style={styles.topicTag}>
              <Text style={styles.topicText}>#TypeScript</Text>
            </View>
            <View style={styles.topicTag}>
              <Text style={styles.topicText}>#Design</Text>
            </View>
            <View style={styles.topicTag}>
              <Text style={styles.topicText}>#Startup</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContent: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    paddingVertical: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  peopleGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  personCard: {
    backgroundColor: '#fff',
    width: '48%',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  personAvatar: {
    fontSize: 40,
    marginBottom: 10,
  },
  personName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  personRole: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  connectButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },
  connectButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  groupItem: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  groupIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  groupMembers: {
    fontSize: 14,
    color: '#666',
  },
  joinButton: {
    backgroundColor: '#34C759',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 15,
  },
  joinButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  topicsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  topicTag: {
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 5,
  },
  topicText: {
    color: '#1976D2',
    fontSize: 14,
    fontWeight: '500',
  },
});
