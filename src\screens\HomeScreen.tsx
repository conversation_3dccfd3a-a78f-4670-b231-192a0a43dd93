import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
  Image,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Colors } from '../constants/colors';
import {
  BORDER_RADIUS,
  FONT_SIZES,
  ICON_SIZES,
  isTablet,
  isWeb,
  SPACING
} from '../constants/dimensions';
import ResponsiveContainer from '../components/ResponsiveContainer';

// Mock data for chats
const mockChats = [
  {
    id: '1',
    name: '<PERSON>',
    lastMessage: 'Hey, how are you doing?',
    time: '2:30 PM',
    unreadCount: 2,
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    isFavorite: false,
  },
  {
    id: '2',
    name: '<PERSON>',
    lastMessage: 'See you tomorrow! 👋',
    time: '1:45 PM',
    unreadCount: 0,
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    isOnline: false,
    isFavorite: true,
  },
  {
    id: '3',
    name: 'Team Group',
    lastMessage: 'Meeting at 3 PM',
    time: '12:15 PM',
    unreadCount: 5,
    avatar: null,
    isOnline: false,
    isFavorite: false,
    isGroup: true,
  },
  {
    id: '4',
    name: 'Sarah Wilson',
    lastMessage: 'Thanks for the help! 🙏',
    time: '11:30 AM',
    unreadCount: 0,
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    isFavorite: false,
  },
  {
    id: '5',
    name: 'Mike Johnson',
    lastMessage: 'Let\'s catch up soon',
    time: 'Yesterday',
    unreadCount: 1,
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    isOnline: false,
    isFavorite: true,
  },
];

export default function HomeScreen() {
  const [searchText, setSearchText] = useState('');
  const [chats, setChats] = useState(mockChats);

  const filteredChats = chats.filter(chat =>
    chat.name.toLowerCase().includes(searchText.toLowerCase())
  );

  const toggleFavorite = (chatId: string) => {
    setChats(prevChats =>
      prevChats.map(chat =>
        chat.id === chatId ? { ...chat, isFavorite: !chat.isFavorite } : chat
      )
    );
  };

  const renderChatItem = (chat: typeof mockChats[0]) => (
    <TouchableOpacity key={chat.id} style={styles.chatItem} activeOpacity={0.7}>
      <View style={styles.avatarContainer}>
        {chat.avatar ? (
          <Image source={{ uri: chat.avatar }} style={styles.avatar} />
        ) : (
          <View style={[styles.avatar, styles.groupAvatar]}>
            <Ionicons name="people" size={ICON_SIZES.md} color={Colors.primary} />
          </View>
        )}
        {chat.isOnline && <View style={styles.onlineIndicator} />}
      </View>

      <View style={styles.chatContent}>
        <View style={styles.chatHeader}>
          <Text style={styles.chatName} numberOfLines={1}>{chat.name}</Text>
          <View style={styles.chatMeta}>
            <Text style={styles.chatTime}>{chat.time}</Text>
            <TouchableOpacity
              onPress={() => toggleFavorite(chat.id)}
              style={styles.favoriteButton}
            >
              <Ionicons
                name={chat.isFavorite ? "heart" : "heart-outline"}
                size={ICON_SIZES.sm}
                color={chat.isFavorite ? Colors.error : Colors.textSecondary}
              />
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.messageRow}>
          <Text style={styles.lastMessage} numberOfLines={1}>
            {chat.lastMessage}
          </Text>
          {chat.unreadCount > 0 && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadText}>
                {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <ResponsiveContainer>
      <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <Text style={styles.headerTitle}>Chats</Text>
          <TouchableOpacity style={styles.headerButton}>
            <Ionicons name="create-outline" size={ICON_SIZES.md} color={Colors.primary} />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={ICON_SIZES.sm} color={Colors.textSecondary} style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search chats..."
            placeholderTextColor="#8E8E93"
            value={searchText}
            onChangeText={setSearchText}
          />
          {searchText.length > 0 && (
            <TouchableOpacity onPress={() => setSearchText('')} style={styles.clearButton}>
              <Ionicons name="close-circle" size={ICON_SIZES.sm} color={Colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Chat List */}
      <ScrollView
        style={styles.chatList}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.chatListContent}
      >
        {filteredChats.length > 0 ? (
          filteredChats.map(renderChatItem)
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="chatbubbles-outline" size={ICON_SIZES.xxl * 2} color={Colors.textTertiary} />
            <Text style={styles.emptyStateText}>
              {searchText ? 'No chats found' : 'No chats yet'}
            </Text>
            <Text style={styles.emptyStateSubtext}>
              {searchText ? 'Try a different search term' : 'Start a conversation to see it here'}
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Floating Action Button */}
      <TouchableOpacity style={styles.fab} activeOpacity={0.8}>
        <Ionicons name="add" size={ICON_SIZES.lg} color={Colors.background} />
      </TouchableOpacity>
    </SafeAreaView>
    </ResponsiveContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.backgroundTertiary,
  },
  header: {
    backgroundColor: Colors.background,
    paddingHorizontal: SPACING.lg,
    paddingTop: Platform.OS === 'ios' ? SPACING.sm : SPACING.md,
    paddingBottom: SPACING.md,
    borderBottomWidth: 0.5,
    borderBottomColor: Colors.separator,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  headerTitle: {
    fontSize: isTablet ? FONT_SIZES.xxxl : FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: Colors.text,
  },
  headerButton: {
    padding: SPACING.sm,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.backgroundTertiary,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
  },
  searchIcon: {
    marginRight: SPACING.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: FONT_SIZES.md,
    color: Colors.text,
  },
  clearButton: {
    padding: SPACING.xs,
  },
  chatList: {
    flex: 1,
  },
  chatListContent: {
    paddingTop: SPACING.sm,
  },
  chatItem: {
    flexDirection: 'row',
    backgroundColor: Colors.background,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 0.5,
    borderBottomColor: Colors.separator,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: SPACING.md,
  },
  avatar: {
    width: isTablet ? 60 : 50,
    height: isTablet ? 60 : 50,
    borderRadius: isTablet ? 30 : 25,
    backgroundColor: Colors.backgroundTertiary,
  },
  groupAvatar: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.online,
    borderWidth: 2,
    borderColor: Colors.background,
  },
  chatContent: {
    flex: 1,
    justifyContent: 'center',
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  chatName: {
    fontSize: isTablet ? FONT_SIZES.lg : FONT_SIZES.md,
    fontWeight: '600',
    color: Colors.text,
    flex: 1,
  },
  chatMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  chatTime: {
    fontSize: FONT_SIZES.sm,
    color: Colors.textSecondary,
    marginRight: SPACING.sm,
  },
  favoriteButton: {
    padding: SPACING.xs,
  },
  messageRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    fontSize: FONT_SIZES.sm,
    color: Colors.textSecondary,
    flex: 1,
  },
  unreadBadge: {
    backgroundColor: Colors.primary,
    borderRadius: BORDER_RADIUS.round,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xs,
  },
  unreadText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: '600',
    color: Colors.background,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: SPACING.xxl * 3,
  },
  emptyStateText: {
    fontSize: isTablet ? FONT_SIZES.lg : FONT_SIZES.md,
    fontWeight: '600',
    color: Colors.textSecondary,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptyStateSubtext: {
    fontSize: FONT_SIZES.sm,
    color: Colors.textTertiary,
    textAlign: 'center',
    paddingHorizontal: SPACING.xxl,
  },
  fab: {
    position: 'absolute',
    bottom: isWeb ? SPACING.xl : SPACING.xxl * 2,
    right: SPACING.lg,
    width: isTablet ? 60 : 50,
    height: isTablet ? 60 : 50,
    borderRadius: isTablet ? 30 : 25,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});
