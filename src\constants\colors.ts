// Custom Color Palette for Chat App
export const Colors = {
  // Primary Colors
  primary: '#007AFF',
  primaryDark: '#0056CC',
  primaryLight: '#4DA3FF',
  
  // Secondary Colors
  secondary: '#34C759',
  secondaryDark: '#28A745',
  secondaryLight: '#5ED670',
  
  // Background Colors
  background: '#FFFFFF',
  backgroundSecondary: '#F8F9FA',
  backgroundTertiary: '#F2F2F7',
  
  // Dark Theme Colors
  backgroundDark: '#000000',
  backgroundSecondaryDark: '#1C1C1E',
  backgroundTertiaryDark: '#2C2C2E',
  
  // Text Colors
  text: '#000000',
  textSecondary: '#6C6C70',
  textTertiary: '#8E8E93',
  textPlaceholder: '#C7C7CC',
  
  // Dark Theme Text Colors
  textDark: '#FFFFFF',
  textSecondaryDark: '#AEAEB2',
  textTertiaryDark: '#8E8E93',
  
  // System Colors
  success: '#34C759',
  warning: '#FF9500',
  error: '#FF3B30',
  info: '#007AFF',
  
  // Chat Colors
  chatBubbleSent: '#007AFF',
  chatBubbleReceived: '#E5E5EA',
  chatBubbleSentDark: '#0A84FF',
  chatBubbleReceivedDark: '#3A3A3C',
  
  // Border Colors
  border: '#C6C6C8',
  borderDark: '#38383A',
  separator: '#E5E5EA',
  separatorDark: '#38383A',
  
  // Tab Bar Colors
  tabBarBackground: '#FFFFFF',
  tabBarBackgroundDark: '#000000',
  tabBarActive: '#007AFF',
  tabBarInactive: '#8E8E93',
  
  // Status Colors
  online: '#34C759',
  offline: '#8E8E93',
  away: '#FF9500',
  
  // Gradient Colors
  gradientStart: '#007AFF',
  gradientEnd: '#5856D6',
  
  // Shadow Colors
  shadow: '#000000',
  shadowLight: 'rgba(0, 0, 0, 0.1)',
  shadowMedium: 'rgba(0, 0, 0, 0.2)',
  shadowDark: 'rgba(0, 0, 0, 0.3)',
};

// Theme configurations
export const LightTheme = {
  background: Colors.background,
  backgroundSecondary: Colors.backgroundSecondary,
  backgroundTertiary: Colors.backgroundTertiary,
  text: Colors.text,
  textSecondary: Colors.textSecondary,
  textTertiary: Colors.textTertiary,
  border: Colors.border,
  separator: Colors.separator,
  tabBarBackground: Colors.tabBarBackground,
  chatBubbleSent: Colors.chatBubbleSent,
  chatBubbleReceived: Colors.chatBubbleReceived,
};

export const DarkTheme = {
  background: Colors.backgroundDark,
  backgroundSecondary: Colors.backgroundSecondaryDark,
  backgroundTertiary: Colors.backgroundTertiaryDark,
  text: Colors.textDark,
  textSecondary: Colors.textSecondaryDark,
  textTertiary: Colors.textTertiaryDark,
  border: Colors.borderDark,
  separator: Colors.separatorDark,
  tabBarBackground: Colors.tabBarBackgroundDark,
  chatBubbleSent: Colors.chatBubbleSentDark,
  chatBubbleReceived: Colors.chatBubbleReceivedDark,
};
