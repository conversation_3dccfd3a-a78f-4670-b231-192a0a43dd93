import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ImageStyle,
} from 'react-native';
import { Colors, Typography, BorderRadius, Layout } from '../constants';

interface AvatarProps {
  source?: { uri: string } | number;
  name?: string;
  size?: 'small' | 'medium' | 'large' | 'xlarge' | number;
  style?: ViewStyle;
  imageStyle?: ImageStyle;
  textStyle?: TextStyle;
  backgroundColor?: string;
  textColor?: string;
  showOnlineIndicator?: boolean;
  isOnline?: boolean;
  borderWidth?: number;
  borderColor?: string;
}

export const Avatar: React.FC<AvatarProps> = ({
  source,
  name = '',
  size = 'medium',
  style,
  imageStyle,
  textStyle,
  backgroundColor,
  textColor,
  showOnlineIndicator = false,
  isOnline = false,
  borderWidth = 0,
  borderColor = Colors.border,
}) => {
  const getSize = (): number => {
    if (typeof size === 'number') return size;
    
    switch (size) {
      case 'small':
        return Layout.avatarSmall;
      case 'medium':
        return Layout.avatar;
      case 'large':
        return Layout.avatarLarge;
      case 'xlarge':
        return Layout.avatarXLarge;
      default:
        return Layout.avatar;
    }
  };

  const getFontSize = (): number => {
    const avatarSize = getSize();
    return Math.round(avatarSize * 0.4);
  };

  const getInitials = (fullName: string): string => {
    if (!fullName) return '';
    
    const names = fullName.trim().split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    }
    
    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  };

  const getBackgroundColor = (): string => {
    if (backgroundColor) return backgroundColor;
    
    // Generate color based on name
    if (name) {
      const colors = [
        Colors.primary,
        Colors.secondary,
        Colors.warning,
        Colors.info,
        '#FF6B6B',
        '#4ECDC4',
        '#45B7D1',
        '#96CEB4',
        '#FFEAA7',
        '#DDA0DD',
        '#98D8C8',
        '#F7DC6F',
      ];
      
      const index = name.charCodeAt(0) % colors.length;
      return colors[index];
    }
    
    return Colors.backgroundSecondary;
  };

  const avatarSize = getSize();
  const fontSize = getFontSize();
  const initials = getInitials(name);

  const containerStyle: ViewStyle = {
    width: avatarSize,
    height: avatarSize,
    borderRadius: BorderRadius.avatar,
    backgroundColor: getBackgroundColor(),
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth,
    borderColor,
    position: 'relative',
  };

  const onlineIndicatorSize = Math.round(avatarSize * 0.25);
  const onlineIndicatorStyle: ViewStyle = {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: onlineIndicatorSize,
    height: onlineIndicatorSize,
    borderRadius: onlineIndicatorSize / 2,
    backgroundColor: isOnline ? Colors.online : Colors.offline,
    borderWidth: 2,
    borderColor: Colors.background,
  };

  return (
    <View style={[containerStyle, style]}>
      {source ? (
        <Image
          source={source}
          style={[
            {
              width: avatarSize - borderWidth * 2,
              height: avatarSize - borderWidth * 2,
              borderRadius: BorderRadius.avatar,
            },
            imageStyle,
          ]}
          resizeMode="cover"
        />
      ) : (
        <Text
          style={[
            {
              fontSize,
              color: textColor || Colors.background,
              fontWeight: '600',
            },
            textStyle,
          ]}
        >
          {initials}
        </Text>
      )}
      
      {showOnlineIndicator && (
        <View style={onlineIndicatorStyle} />
      )}
    </View>
  );
};

// Preset avatar sizes for convenience
export const AvatarSizes = {
  small: Layout.avatarSmall,
  medium: Layout.avatar,
  large: Layout.avatarLarge,
  xlarge: Layout.avatarXLarge,
} as const;
