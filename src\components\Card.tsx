import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  TouchableOpacityProps,
} from 'react-native';
import { Colors, BorderRadius, Shadow, Spacing } from '../constants';

interface CardProps extends TouchableOpacityProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'small' | 'medium' | 'large';
  margin?: 'none' | 'small' | 'medium' | 'large';
  borderRadius?: 'none' | 'small' | 'medium' | 'large' | 'full';
  shadow?: 'none' | 'small' | 'medium' | 'large';
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  onPress?: () => void;
  disabled?: boolean;
  style?: ViewStyle;
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'medium',
  margin = 'none',
  borderRadius = 'medium',
  shadow = 'small',
  backgroundColor,
  borderColor,
  borderWidth,
  onPress,
  disabled = false,
  style,
  ...props
}) => {
  const getVariantStyle = (): ViewStyle => {
    switch (variant) {
      case 'elevated':
        return {
          backgroundColor: backgroundColor || Colors.background,
          ...Shadow.md,
        };
      case 'outlined':
        return {
          backgroundColor: backgroundColor || Colors.background,
          borderWidth: borderWidth || 1,
          borderColor: borderColor || Colors.border,
        };
      case 'filled':
        return {
          backgroundColor: backgroundColor || Colors.backgroundSecondary,
        };
      default:
        return {
          backgroundColor: backgroundColor || Colors.background,
          ...getShadowStyle(),
        };
    }
  };

  const getPaddingStyle = (): ViewStyle => {
    switch (padding) {
      case 'none':
        return {};
      case 'small':
        return { padding: Spacing.sm };
      case 'medium':
        return { padding: Spacing.base };
      case 'large':
        return { padding: Spacing.xl };
      default:
        return { padding: Spacing.base };
    }
  };

  const getMarginStyle = (): ViewStyle => {
    switch (margin) {
      case 'none':
        return {};
      case 'small':
        return { margin: Spacing.sm };
      case 'medium':
        return { margin: Spacing.base };
      case 'large':
        return { margin: Spacing.xl };
      default:
        return {};
    }
  };

  const getBorderRadiusStyle = (): ViewStyle => {
    switch (borderRadius) {
      case 'none':
        return { borderRadius: 0 };
      case 'small':
        return { borderRadius: BorderRadius.sm };
      case 'medium':
        return { borderRadius: BorderRadius.md };
      case 'large':
        return { borderRadius: BorderRadius.lg };
      case 'full':
        return { borderRadius: BorderRadius.full };
      default:
        return { borderRadius: BorderRadius.md };
    }
  };

  const getShadowStyle = (): ViewStyle => {
    switch (shadow) {
      case 'none':
        return Shadow.none;
      case 'small':
        return Shadow.sm;
      case 'medium':
        return Shadow.md;
      case 'large':
        return Shadow.lg;
      default:
        return Shadow.sm;
    }
  };

  const cardStyle: ViewStyle = {
    ...styles.base,
    ...getVariantStyle(),
    ...getPaddingStyle(),
    ...getMarginStyle(),
    ...getBorderRadiusStyle(),
  };

  if (onPress) {
    return (
      <TouchableOpacity
        style={[cardStyle, disabled && styles.disabled, style]}
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.7}
        {...props}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={[cardStyle, style]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  base: {
    overflow: 'hidden',
  },
  disabled: {
    opacity: 0.6,
  },
});

// Preset card configurations
export const CardPresets = {
  chatItem: {
    variant: 'default' as const,
    padding: 'medium' as const,
    margin: 'none' as const,
    borderRadius: 'medium' as const,
    shadow: 'small' as const,
  },
  profileCard: {
    variant: 'elevated' as const,
    padding: 'large' as const,
    margin: 'medium' as const,
    borderRadius: 'large' as const,
    shadow: 'medium' as const,
  },
  messageCard: {
    variant: 'filled' as const,
    padding: 'small' as const,
    margin: 'none' as const,
    borderRadius: 'medium' as const,
    shadow: 'none' as const,
  },
} as const;
