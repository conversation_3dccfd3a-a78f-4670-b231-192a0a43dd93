import React from 'react';
import {
  View,
  ActivityIndicator,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Colors, Typography, Spacing } from '../constants';

interface LoadingIndicatorProps {
  size?: 'small' | 'large' | number;
  color?: string;
  text?: string;
  variant?: 'default' | 'overlay' | 'inline';
  style?: ViewStyle;
  textStyle?: TextStyle;
  backgroundColor?: string;
}

export const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  size = 'large',
  color = Colors.primary,
  text,
  variant = 'default',
  style,
  textStyle,
  backgroundColor,
}) => {
  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = styles.container;

    switch (variant) {
      case 'overlay':
        return {
          ...baseStyle,
          ...styles.overlay,
          backgroundColor: backgroundColor || 'rgba(0, 0, 0, 0.5)',
        };
      case 'inline':
        return {
          ...baseStyle,
          ...styles.inline,
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: backgroundColor || 'transparent',
        };
    }
  };

  return (
    <View style={[getContainerStyle(), style]}>
      <ActivityIndicator size={size} color={color} />
      {text && (
        <Text style={[styles.text, textStyle]}>
          {text}
        </Text>
      )}
    </View>
  );
};

// Skeleton loading component
interface SkeletonProps {
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: ViewStyle;
  animated?: boolean;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  width = '100%',
  height = 20,
  borderRadius = 4,
  style,
  animated = true,
}) => {
  return (
    <View
      style={[
        styles.skeleton,
        {
          width,
          height,
          borderRadius,
        },
        style,
      ]}
    />
  );
};

// Skeleton text lines
interface SkeletonTextProps {
  lines?: number;
  lineHeight?: number;
  lastLineWidth?: string;
  style?: ViewStyle;
}

export const SkeletonText: React.FC<SkeletonTextProps> = ({
  lines = 3,
  lineHeight = 16,
  lastLineWidth = '60%',
  style,
}) => {
  return (
    <View style={[styles.skeletonTextContainer, style]}>
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          height={lineHeight}
          width={index === lines - 1 ? lastLineWidth : '100%'}
          style={index < lines - 1 ? styles.skeletonTextLine : undefined}
        />
      ))}
    </View>
  );
};

// Chat message skeleton
export const ChatMessageSkeleton: React.FC<{ isOwn?: boolean }> = ({
  isOwn = false,
}) => {
  return (
    <View style={[styles.chatSkeletonContainer, isOwn && styles.chatSkeletonOwn]}>
      {!isOwn && (
        <Skeleton
          width={32}
          height={32}
          borderRadius={16}
          style={styles.chatSkeletonAvatar}
        />
      )}
      <View style={styles.chatSkeletonBubble}>
        <SkeletonText lines={2} lineHeight={14} lastLineWidth="70%" />
      </View>
    </View>
  );
};

// Loading states for different scenarios
export const LoadingStates = {
  // Full screen loading
  fullScreen: (text?: string) => (
    <LoadingIndicator
      variant="overlay"
      text={text || 'Loading...'}
      style={styles.fullScreen}
    />
  ),

  // Inline loading for buttons
  button: (color?: string) => (
    <LoadingIndicator
      size="small"
      color={color || Colors.background}
      variant="inline"
    />
  ),

  // Chat loading
  chat: () => (
    <View style={styles.chatLoadingContainer}>
      {Array.from({ length: 5 }).map((_, index) => (
        <ChatMessageSkeleton
          key={index}
          isOwn={index % 3 === 0}
        />
      ))}
    </View>
  ),

  // List loading
  list: (itemCount: number = 5) => (
    <View style={styles.listLoadingContainer}>
      {Array.from({ length: itemCount }).map((_, index) => (
        <View key={index} style={styles.listSkeletonItem}>
          <Skeleton
            width={40}
            height={40}
            borderRadius={20}
            style={styles.listSkeletonAvatar}
          />
          <View style={styles.listSkeletonContent}>
            <Skeleton height={16} width="60%" />
            <Skeleton
              height={12}
              width="80%"
              style={styles.listSkeletonSubtext}
            />
          </View>
        </View>
      ))}
    </View>
  ),
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  
  inline: {
    padding: Spacing.sm,
  },
  
  fullScreen: {
    flex: 1,
  },
  
  text: {
    ...Typography.body,
    color: Colors.textSecondary,
    marginTop: Spacing.md,
    textAlign: 'center',
  },
  
  // Skeleton styles
  skeleton: {
    backgroundColor: Colors.backgroundSecondary,
  },
  
  skeletonTextContainer: {
    gap: Spacing.xs,
  },
  
  skeletonTextLine: {
    marginBottom: Spacing.xs,
  },
  
  // Chat skeleton styles
  chatSkeletonContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.md,
    paddingHorizontal: Spacing.base,
  },
  
  chatSkeletonOwn: {
    justifyContent: 'flex-end',
  },
  
  chatSkeletonAvatar: {
    marginRight: Spacing.sm,
  },
  
  chatSkeletonBubble: {
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 18,
    padding: Spacing.md,
    maxWidth: '70%',
  },
  
  // Chat loading container
  chatLoadingContainer: {
    flex: 1,
    paddingVertical: Spacing.lg,
  },
  
  // List skeleton styles
  listLoadingContainer: {
    padding: Spacing.base,
  },
  
  listSkeletonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.base,
  },
  
  listSkeletonAvatar: {
    marginRight: Spacing.md,
  },
  
  listSkeletonContent: {
    flex: 1,
    gap: Spacing.xs,
  },
  
  listSkeletonSubtext: {
    marginTop: Spacing.xs,
  },
});
