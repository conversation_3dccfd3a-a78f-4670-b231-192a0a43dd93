// Spacing system based on 4px grid
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  base: 16,
  lg: 20,
  xl: 24,
  '2xl': 32,
  '3xl': 40,
  '4xl': 48,
  '5xl': 64,
  '6xl': 80,
  '7xl': 96,
  '8xl': 128,
};

// Padding presets
export const Padding = {
  // Screen padding
  screen: Spacing.base,
  screenHorizontal: Spacing.base,
  screenVertical: Spacing.lg,
  
  // Container padding
  container: Spacing.base,
  containerSmall: Spacing.md,
  containerLarge: Spacing.xl,
  
  // Card padding
  card: Spacing.base,
  cardSmall: Spacing.md,
  cardLarge: Spacing.xl,
  
  // Button padding
  buttonVertical: Spacing.md,
  buttonHorizontal: Spacing.lg,
  buttonSmallVertical: Spacing.sm,
  buttonSmallHorizontal: Spacing.md,
  buttonLargeVertical: Spacing.base,
  buttonLargeHorizontal: Spacing.xl,
  
  // Input padding
  input: Spacing.base,
  inputSmall: Spacing.md,
  inputLarge: Spacing.lg,
  
  // Chat specific padding
  chatBubble: Spacing.md,
  chatBubbleHorizontal: Spacing.base,
  chatBubbleVertical: Spacing.sm,
  chatInput: Spacing.base,
  chatScreen: Spacing.base,
};

// Margin presets
export const Margin = {
  // Element margins
  elementSmall: Spacing.xs,
  element: Spacing.sm,
  elementLarge: Spacing.md,
  
  // Section margins
  section: Spacing.lg,
  sectionLarge: Spacing.xl,
  
  // Component margins
  component: Spacing.base,
  componentSmall: Spacing.md,
  componentLarge: Spacing.xl,
  
  // Chat specific margins
  chatMessage: Spacing.xs,
  chatBubble: Spacing.sm,
  chatSection: Spacing.md,
};

// Gap presets for flexbox/grid layouts
export const Gap = {
  xs: Spacing.xs,
  sm: Spacing.sm,
  md: Spacing.md,
  base: Spacing.base,
  lg: Spacing.lg,
  xl: Spacing.xl,
  '2xl': Spacing['2xl'],
};

// Border radius
export const BorderRadius = {
  none: 0,
  xs: 2,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  full: 9999,
  
  // Component specific radius
  button: 8,
  buttonSmall: 6,
  buttonLarge: 12,
  card: 12,
  input: 8,
  modal: 16,
  chatBubble: 18,
  avatar: 9999,
  image: 8,
};

// Shadow presets
export const Shadow = {
  none: {
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sm: {
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  base: {
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  md: {
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  xl: {
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 12,
  },
  '2xl': {
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 16,
  },
};

// Layout dimensions
export const Layout = {
  // Header heights
  headerHeight: 56,
  headerHeightLarge: 64,
  
  // Tab bar heights
  tabBarHeight: 60,
  tabBarHeightSafe: 84, // with safe area
  
  // Button heights
  buttonHeight: 48,
  buttonHeightSmall: 36,
  buttonHeightLarge: 56,
  
  // Input heights
  inputHeight: 48,
  inputHeightSmall: 36,
  inputHeightLarge: 56,
  
  // Avatar sizes
  avatarSmall: 32,
  avatar: 40,
  avatarLarge: 56,
  avatarXLarge: 80,
  
  // Icon sizes
  iconSmall: 16,
  icon: 20,
  iconMedium: 24,
  iconLarge: 28,
  iconXLarge: 32,
  
  // Chat specific dimensions
  chatBubbleMinHeight: 36,
  chatInputHeight: 48,
  chatInputMaxHeight: 120,
  
  // Screen dimensions (will be set dynamically)
  window: {
    width: 0,
    height: 0,
  },
  screen: {
    width: 0,
    height: 0,
  },
};
